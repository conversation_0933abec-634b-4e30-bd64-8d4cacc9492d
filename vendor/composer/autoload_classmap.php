<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Adbar\\Dot' => $vendorDir . '/adbario/php-dot-notation/src/Dot.php',
    'AlibabaCloud\\Tea\\Exception\\TeaError' => $vendorDir . '/alibabacloud/tea/src/Exception/TeaError.php',
    'AlibabaCloud\\Tea\\Exception\\TeaRetryError' => $vendorDir . '/alibabacloud/tea/src/Exception/TeaRetryError.php',
    'AlibabaCloud\\Tea\\Exception\\TeaUnableRetryError' => $vendorDir . '/alibabacloud/tea/src/Exception/TeaUnableRetryError.php',
    'AlibabaCloud\\Tea\\FileForm\\FileForm' => $vendorDir . '/alibabacloud/tea-fileform/src/FileForm.php',
    'AlibabaCloud\\Tea\\FileForm\\FileFormStream' => $vendorDir . '/alibabacloud/tea-fileform/src/FileFormStream.php',
    'AlibabaCloud\\Tea\\FileForm\\FileForm\\FileField' => $vendorDir . '/alibabacloud/tea-fileform/src/FileForm/FileField.php',
    'AlibabaCloud\\Tea\\Helper' => $vendorDir . '/alibabacloud/tea/src/Helper.php',
    'AlibabaCloud\\Tea\\Model' => $vendorDir . '/alibabacloud/tea/src/Model.php',
    'AlibabaCloud\\Tea\\Parameter' => $vendorDir . '/alibabacloud/tea/src/Parameter.php',
    'AlibabaCloud\\Tea\\Request' => $vendorDir . '/alibabacloud/tea/src/Request.php',
    'AlibabaCloud\\Tea\\Response' => $vendorDir . '/alibabacloud/tea/src/Response.php',
    'AlibabaCloud\\Tea\\Tea' => $vendorDir . '/alibabacloud/tea/src/Tea.php',
    'Alipay\\EasySDK\\Base\\Image\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/Image/Client.php',
    'Alipay\\EasySDK\\Base\\Image\\Models\\AlipayOfflineMaterialImageUploadResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/Image/Models/AlipayOfflineMaterialImageUploadResponse.php',
    'Alipay\\EasySDK\\Base\\OAuth\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/OAuth/Client.php',
    'Alipay\\EasySDK\\Base\\OAuth\\Models\\AlipaySystemOauthTokenResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/OAuth/Models/AlipaySystemOauthTokenResponse.php',
    'Alipay\\EasySDK\\Base\\Qrcode\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/Qrcode/Client.php',
    'Alipay\\EasySDK\\Base\\Qrcode\\Models\\AlipayOpenAppQrcodeCreateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/Qrcode/Models/AlipayOpenAppQrcodeCreateResponse.php',
    'Alipay\\EasySDK\\Base\\Video\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/Video/Client.php',
    'Alipay\\EasySDK\\Base\\Video\\Models\\AlipayOfflineMaterialImageUploadResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Base/Video/Models/AlipayOfflineMaterialImageUploadResponse.php',
    'Alipay\\EasySDK\\Kernel\\AlipayConstants' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/AlipayConstants.php',
    'Alipay\\EasySDK\\Kernel\\CertEnvironment' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/CertEnvironment.php',
    'Alipay\\EasySDK\\Kernel\\Config' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Config.php',
    'Alipay\\EasySDK\\Kernel\\EasySDKKernel' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/EasySDKKernel.php',
    'Alipay\\EasySDK\\Kernel\\Exceptions\\RuntimeException' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Exceptions/RuntimeException.php',
    'Alipay\\EasySDK\\Kernel\\Factory' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Factory.php',
    'Alipay\\EasySDK\\Kernel\\MultipleFactory' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/MultipleFactory.php',
    'Alipay\\EasySDK\\Kernel\\Util\\AES' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/AES.php',
    'Alipay\\EasySDK\\Kernel\\Util\\AlipayEncrypt' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/AlipayEncrypt.php',
    'Alipay\\EasySDK\\Kernel\\Util\\AntCertificationUtil' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/AntCertificationUtil.php',
    'Alipay\\EasySDK\\Kernel\\Util\\JsonUtil' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/JsonUtil.php',
    'Alipay\\EasySDK\\Kernel\\Util\\PageUtil' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/PageUtil.php',
    'Alipay\\EasySDK\\Kernel\\Util\\ResponseChecker' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/ResponseChecker.php',
    'Alipay\\EasySDK\\Kernel\\Util\\SignContentExtractor' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/SignContentExtractor.php',
    'Alipay\\EasySDK\\Kernel\\Util\\Signer' => $vendorDir . '/alipaysdk/easysdk/php/src/Kernel/Util/Signer.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Client.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicLifeMsgRecallResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicLifeMsgRecallResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicMessageContentCreateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicMessageContentCreateResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicMessageContentModifyResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicMessageContentModifyResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicMessageSingleSendResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicMessageSingleSendResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicMessageTotalSendResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicMessageTotalSendResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicSettingCategoryQueryResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicSettingCategoryQueryResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\AlipayOpenPublicTemplateMessageIndustryModifyResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/AlipayOpenPublicTemplateMessageIndustryModifyResponse.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\Article' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/Article.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\Context' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/Context.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\Keyword' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/Keyword.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\Template' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/Template.php',
    'Alipay\\EasySDK\\Marketing\\OpenLife\\Models\\Text' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/OpenLife/Models/Text.php',
    'Alipay\\EasySDK\\Marketing\\Pass\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/Pass/Client.php',
    'Alipay\\EasySDK\\Marketing\\Pass\\Models\\AlipayPassInstanceAddResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/Pass/Models/AlipayPassInstanceAddResponse.php',
    'Alipay\\EasySDK\\Marketing\\Pass\\Models\\AlipayPassInstanceUpdateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/Pass/Models/AlipayPassInstanceUpdateResponse.php',
    'Alipay\\EasySDK\\Marketing\\Pass\\Models\\AlipayPassTemplateAddResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/Pass/Models/AlipayPassTemplateAddResponse.php',
    'Alipay\\EasySDK\\Marketing\\Pass\\Models\\AlipayPassTemplateUpdateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/Pass/Models/AlipayPassTemplateUpdateResponse.php',
    'Alipay\\EasySDK\\Marketing\\TemplateMessage\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/TemplateMessage/Client.php',
    'Alipay\\EasySDK\\Marketing\\TemplateMessage\\Models\\AlipayOpenAppMiniTemplatemessageSendResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Marketing/TemplateMessage/Models/AlipayOpenAppMiniTemplatemessageSendResponse.php',
    'Alipay\\EasySDK\\Member\\Identification\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Member/Identification/Client.php',
    'Alipay\\EasySDK\\Member\\Identification\\Models\\AlipayUserCertifyOpenCertifyResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Member/Identification/Models/AlipayUserCertifyOpenCertifyResponse.php',
    'Alipay\\EasySDK\\Member\\Identification\\Models\\AlipayUserCertifyOpenInitializeResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Member/Identification/Models/AlipayUserCertifyOpenInitializeResponse.php',
    'Alipay\\EasySDK\\Member\\Identification\\Models\\AlipayUserCertifyOpenQueryResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Member/Identification/Models/AlipayUserCertifyOpenQueryResponse.php',
    'Alipay\\EasySDK\\Member\\Identification\\Models\\IdentityParam' => $vendorDir . '/alipaysdk/easysdk/php/src/Member/Identification/Models/IdentityParam.php',
    'Alipay\\EasySDK\\Member\\Identification\\Models\\MerchantConfig' => $vendorDir . '/alipaysdk/easysdk/php/src/Member/Identification/Models/MerchantConfig.php',
    'Alipay\\EasySDK\\Payment\\App\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/App/Client.php',
    'Alipay\\EasySDK\\Payment\\App\\Models\\AlipayTradeAppPayResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/App/Models/AlipayTradeAppPayResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Client.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayDataDataserviceBillDownloadurlQueryResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayDataDataserviceBillDownloadurlQueryResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayTradeCancelResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayTradeCancelResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayTradeCloseResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayTradeCloseResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayTradeCreateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayTradeCreateResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayTradeFastpayRefundQueryResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayTradeFastpayRefundQueryResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayTradeQueryResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayTradeQueryResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\AlipayTradeRefundResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/AlipayTradeRefundResponse.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\PresetPayToolInfo' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/PresetPayToolInfo.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\RefundRoyaltyResult' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/RefundRoyaltyResult.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\TradeFundBill' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/TradeFundBill.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\TradeSettleDetail' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/TradeSettleDetail.php',
    'Alipay\\EasySDK\\Payment\\Common\\Models\\TradeSettleInfo' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Common/Models/TradeSettleInfo.php',
    'Alipay\\EasySDK\\Payment\\FaceToFace\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/FaceToFace/Client.php',
    'Alipay\\EasySDK\\Payment\\FaceToFace\\Models\\AlipayTradePayResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/FaceToFace/Models/AlipayTradePayResponse.php',
    'Alipay\\EasySDK\\Payment\\FaceToFace\\Models\\AlipayTradePrecreateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/FaceToFace/Models/AlipayTradePrecreateResponse.php',
    'Alipay\\EasySDK\\Payment\\FaceToFace\\Models\\TradeFundBill' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/FaceToFace/Models/TradeFundBill.php',
    'Alipay\\EasySDK\\Payment\\FaceToFace\\Models\\VoucherDetail' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/FaceToFace/Models/VoucherDetail.php',
    'Alipay\\EasySDK\\Payment\\Huabei\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Huabei/Client.php',
    'Alipay\\EasySDK\\Payment\\Huabei\\Models\\AlipayTradeCreateResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Huabei/Models/AlipayTradeCreateResponse.php',
    'Alipay\\EasySDK\\Payment\\Huabei\\Models\\HuabeiConfig' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Huabei/Models/HuabeiConfig.php',
    'Alipay\\EasySDK\\Payment\\Page\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Page/Client.php',
    'Alipay\\EasySDK\\Payment\\Page\\Models\\AlipayTradePagePayResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Page/Models/AlipayTradePagePayResponse.php',
    'Alipay\\EasySDK\\Payment\\Wap\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Wap/Client.php',
    'Alipay\\EasySDK\\Payment\\Wap\\Models\\AlipayTradeWapPayResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Payment/Wap/Models/AlipayTradeWapPayResponse.php',
    'Alipay\\EasySDK\\Security\\TextRisk\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Security/TextRisk/Client.php',
    'Alipay\\EasySDK\\Security\\TextRisk\\Models\\AlipaySecurityRiskContentDetectResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Security/TextRisk/Models/AlipaySecurityRiskContentDetectResponse.php',
    'Alipay\\EasySDK\\Util\\AES\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Util/AES/Client.php',
    'Alipay\\EasySDK\\Util\\Generic\\Client' => $vendorDir . '/alipaysdk/easysdk/php/src/Util/Generic/Client.php',
    'Alipay\\EasySDK\\Util\\Generic\\Models\\AlipayOpenApiGenericResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Util/Generic/Models/AlipayOpenApiGenericResponse.php',
    'Alipay\\EasySDK\\Util\\Generic\\Models\\AlipayOpenApiGenericSDKResponse' => $vendorDir . '/alipaysdk/easysdk/php/src/Util/Generic/Models/AlipayOpenApiGenericSDKResponse.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'GuzzleHttp\\BodySummarizer' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizer.php',
    'GuzzleHttp\\BodySummarizerInterface' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'GuzzleHttp\\Client' => $vendorDir . '/guzzlehttp/guzzle/src/Client.php',
    'GuzzleHttp\\ClientInterface' => $vendorDir . '/guzzlehttp/guzzle/src/ClientInterface.php',
    'GuzzleHttp\\ClientTrait' => $vendorDir . '/guzzlehttp/guzzle/src/ClientTrait.php',
    'GuzzleHttp\\Cookie\\CookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'GuzzleHttp\\Cookie\\CookieJarInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'GuzzleHttp\\Cookie\\FileCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'GuzzleHttp\\Cookie\\SessionCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'GuzzleHttp\\Cookie\\SetCookie' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'GuzzleHttp\\Exception\\BadResponseException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'GuzzleHttp\\Exception\\ClientException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'GuzzleHttp\\Exception\\ConnectException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'GuzzleHttp\\Exception\\GuzzleException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'GuzzleHttp\\Exception\\InvalidArgumentException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'GuzzleHttp\\Exception\\RequestException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'GuzzleHttp\\Exception\\ServerException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'GuzzleHttp\\Exception\\TooManyRedirectsException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'GuzzleHttp\\Exception\\TransferException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'GuzzleHttp\\HandlerStack' => $vendorDir . '/guzzlehttp/guzzle/src/HandlerStack.php',
    'GuzzleHttp\\Handler\\CurlFactory' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'GuzzleHttp\\Handler\\CurlFactoryInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'GuzzleHttp\\Handler\\CurlHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'GuzzleHttp\\Handler\\CurlMultiHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'GuzzleHttp\\Handler\\EasyHandle' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'GuzzleHttp\\Handler\\HeaderProcessor' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'GuzzleHttp\\Handler\\MockHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'GuzzleHttp\\Handler\\Proxy' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'GuzzleHttp\\Handler\\StreamHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'GuzzleHttp\\MessageFormatter' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatter.php',
    'GuzzleHttp\\MessageFormatterInterface' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'GuzzleHttp\\Middleware' => $vendorDir . '/guzzlehttp/guzzle/src/Middleware.php',
    'GuzzleHttp\\Pool' => $vendorDir . '/guzzlehttp/guzzle/src/Pool.php',
    'GuzzleHttp\\PrepareBodyMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'GuzzleHttp\\Promise\\AggregateException' => $vendorDir . '/guzzlehttp/promises/src/AggregateException.php',
    'GuzzleHttp\\Promise\\CancellationException' => $vendorDir . '/guzzlehttp/promises/src/CancellationException.php',
    'GuzzleHttp\\Promise\\Coroutine' => $vendorDir . '/guzzlehttp/promises/src/Coroutine.php',
    'GuzzleHttp\\Promise\\Create' => $vendorDir . '/guzzlehttp/promises/src/Create.php',
    'GuzzleHttp\\Promise\\Each' => $vendorDir . '/guzzlehttp/promises/src/Each.php',
    'GuzzleHttp\\Promise\\EachPromise' => $vendorDir . '/guzzlehttp/promises/src/EachPromise.php',
    'GuzzleHttp\\Promise\\FulfilledPromise' => $vendorDir . '/guzzlehttp/promises/src/FulfilledPromise.php',
    'GuzzleHttp\\Promise\\Is' => $vendorDir . '/guzzlehttp/promises/src/Is.php',
    'GuzzleHttp\\Promise\\Promise' => $vendorDir . '/guzzlehttp/promises/src/Promise.php',
    'GuzzleHttp\\Promise\\PromiseInterface' => $vendorDir . '/guzzlehttp/promises/src/PromiseInterface.php',
    'GuzzleHttp\\Promise\\PromisorInterface' => $vendorDir . '/guzzlehttp/promises/src/PromisorInterface.php',
    'GuzzleHttp\\Promise\\RejectedPromise' => $vendorDir . '/guzzlehttp/promises/src/RejectedPromise.php',
    'GuzzleHttp\\Promise\\RejectionException' => $vendorDir . '/guzzlehttp/promises/src/RejectionException.php',
    'GuzzleHttp\\Promise\\TaskQueue' => $vendorDir . '/guzzlehttp/promises/src/TaskQueue.php',
    'GuzzleHttp\\Promise\\TaskQueueInterface' => $vendorDir . '/guzzlehttp/promises/src/TaskQueueInterface.php',
    'GuzzleHttp\\Promise\\Utils' => $vendorDir . '/guzzlehttp/promises/src/Utils.php',
    'GuzzleHttp\\Psr7\\AppendStream' => $vendorDir . '/guzzlehttp/psr7/src/AppendStream.php',
    'GuzzleHttp\\Psr7\\BufferStream' => $vendorDir . '/guzzlehttp/psr7/src/BufferStream.php',
    'GuzzleHttp\\Psr7\\CachingStream' => $vendorDir . '/guzzlehttp/psr7/src/CachingStream.php',
    'GuzzleHttp\\Psr7\\DroppingStream' => $vendorDir . '/guzzlehttp/psr7/src/DroppingStream.php',
    'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $vendorDir . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'GuzzleHttp\\Psr7\\FnStream' => $vendorDir . '/guzzlehttp/psr7/src/FnStream.php',
    'GuzzleHttp\\Psr7\\Header' => $vendorDir . '/guzzlehttp/psr7/src/Header.php',
    'GuzzleHttp\\Psr7\\HttpFactory' => $vendorDir . '/guzzlehttp/psr7/src/HttpFactory.php',
    'GuzzleHttp\\Psr7\\InflateStream' => $vendorDir . '/guzzlehttp/psr7/src/InflateStream.php',
    'GuzzleHttp\\Psr7\\LazyOpenStream' => $vendorDir . '/guzzlehttp/psr7/src/LazyOpenStream.php',
    'GuzzleHttp\\Psr7\\LimitStream' => $vendorDir . '/guzzlehttp/psr7/src/LimitStream.php',
    'GuzzleHttp\\Psr7\\Message' => $vendorDir . '/guzzlehttp/psr7/src/Message.php',
    'GuzzleHttp\\Psr7\\MessageTrait' => $vendorDir . '/guzzlehttp/psr7/src/MessageTrait.php',
    'GuzzleHttp\\Psr7\\MimeType' => $vendorDir . '/guzzlehttp/psr7/src/MimeType.php',
    'GuzzleHttp\\Psr7\\MultipartStream' => $vendorDir . '/guzzlehttp/psr7/src/MultipartStream.php',
    'GuzzleHttp\\Psr7\\NoSeekStream' => $vendorDir . '/guzzlehttp/psr7/src/NoSeekStream.php',
    'GuzzleHttp\\Psr7\\PumpStream' => $vendorDir . '/guzzlehttp/psr7/src/PumpStream.php',
    'GuzzleHttp\\Psr7\\Query' => $vendorDir . '/guzzlehttp/psr7/src/Query.php',
    'GuzzleHttp\\Psr7\\Request' => $vendorDir . '/guzzlehttp/psr7/src/Request.php',
    'GuzzleHttp\\Psr7\\Response' => $vendorDir . '/guzzlehttp/psr7/src/Response.php',
    'GuzzleHttp\\Psr7\\Rfc7230' => $vendorDir . '/guzzlehttp/psr7/src/Rfc7230.php',
    'GuzzleHttp\\Psr7\\ServerRequest' => $vendorDir . '/guzzlehttp/psr7/src/ServerRequest.php',
    'GuzzleHttp\\Psr7\\Stream' => $vendorDir . '/guzzlehttp/psr7/src/Stream.php',
    'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $vendorDir . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'GuzzleHttp\\Psr7\\StreamWrapper' => $vendorDir . '/guzzlehttp/psr7/src/StreamWrapper.php',
    'GuzzleHttp\\Psr7\\UploadedFile' => $vendorDir . '/guzzlehttp/psr7/src/UploadedFile.php',
    'GuzzleHttp\\Psr7\\Uri' => $vendorDir . '/guzzlehttp/psr7/src/Uri.php',
    'GuzzleHttp\\Psr7\\UriComparator' => $vendorDir . '/guzzlehttp/psr7/src/UriComparator.php',
    'GuzzleHttp\\Psr7\\UriNormalizer' => $vendorDir . '/guzzlehttp/psr7/src/UriNormalizer.php',
    'GuzzleHttp\\Psr7\\UriResolver' => $vendorDir . '/guzzlehttp/psr7/src/UriResolver.php',
    'GuzzleHttp\\Psr7\\Utils' => $vendorDir . '/guzzlehttp/psr7/src/Utils.php',
    'GuzzleHttp\\RedirectMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'GuzzleHttp\\RequestOptions' => $vendorDir . '/guzzlehttp/guzzle/src/RequestOptions.php',
    'GuzzleHttp\\RetryMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'GuzzleHttp\\TransferStats' => $vendorDir . '/guzzlehttp/guzzle/src/TransferStats.php',
    'GuzzleHttp\\Utils' => $vendorDir . '/guzzlehttp/guzzle/src/Utils.php',
    'Psr\\Http\\Client\\ClientExceptionInterface' => $vendorDir . '/psr/http-client/src/ClientExceptionInterface.php',
    'Psr\\Http\\Client\\ClientInterface' => $vendorDir . '/psr/http-client/src/ClientInterface.php',
    'Psr\\Http\\Client\\NetworkExceptionInterface' => $vendorDir . '/psr/http-client/src/NetworkExceptionInterface.php',
    'Psr\\Http\\Client\\RequestExceptionInterface' => $vendorDir . '/psr/http-client/src/RequestExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
);
