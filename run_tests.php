<?php
/**
 * 主测试脚本
 * 
 * 统一运行所有测试模块
 * 
 * <AUTHOR>
 * @version 1.0
 */

require_once 'vendor/autoload.php';
require_once 'tests/TestRunner.php';
require_once 'tests/BasicTests.php';
require_once 'tests/ApiTests.php';
require_once 'tests/WebTests.php';
require_once 'tests/PerformanceTests.php';

function showMenu()
{
    echo "\n";
    echo "=== 支付宝数据同步系统测试套件 ===\n";
    echo "1. 运行所有测试\n";
    echo "2. 基础功能测试\n";
    echo "3. API功能测试\n";
    echo "4. Web界面测试\n";
    echo "5. 性能测试\n";
    echo "6. 快速检查\n";
    echo "7. 查看测试历史\n";
    echo "8. 清理测试数据\n";
    echo "9. 列出所有测试\n";
    echo "0. 退出\n";
    echo "================================\n";
    echo "请选择操作: ";
}

function runAllTests()
{
    $runner = new TestRunner();
    
    // 注册所有测试
    registerBasicTests($runner);
    registerApiTests($runner);
    registerWebTests($runner);
    registerPerformanceTests($runner);
    
    return $runner->runAll();
}

function registerBasicTests($runner)
{
    $runner->addTest('config', [BasicTests::class, 'testConfiguration'], '配置文件测试');
    $runner->addTest('db_connection', [BasicTests::class, 'testDatabaseConnection'], '数据库连接测试');
    $runner->addTest('table_structure', [BasicTests::class, 'testTableStructure'], '表结构测试');
    $runner->addTest('data_operations', [BasicTests::class, 'testDataOperations'], '数据操作测试');
    $runner->addTest('system_services', [BasicTests::class, 'testSystemServices'], '系统服务测试');
    $runner->addTest('file_permissions', [BasicTests::class, 'testFilePermissions'], '文件权限测试');
}

function registerApiTests($runner)
{
    $runner->addTest('alipay_config', [ApiTests::class, 'testAlipayConfig'], '支付宝配置测试');
    $runner->addTest('bill_fetcher_init', [ApiTests::class, 'testBillFetcherInit'], 'BillFetcher初始化测试');
    $runner->addTest('data_validation', [ApiTests::class, 'testDataValidation'], '数据验证测试');
    $runner->addTest('data_deduplication', [ApiTests::class, 'testDataDeduplication'], '数据去重测试');
    $runner->addTest('date_range_handling', [ApiTests::class, 'testDateRangeHandling'], '日期范围处理测试');
    $runner->addTest('error_handling', [ApiTests::class, 'testErrorHandling'], '错误处理测试');
    $runner->addTest('logging', [ApiTests::class, 'testLogging'], '日志记录测试');
}

function registerWebTests($runner)
{
    $runner->addTest('web_files_exist', [WebTests::class, 'testWebFilesExist'], 'Web文件存在性测试');
    $runner->addTest('web_file_syntax', [WebTests::class, 'testWebFileSyntax'], 'Web文件语法测试');
    $runner->addTest('web_page_structure', [WebTests::class, 'testWebPageStructure'], 'Web页面结构测试');
    $runner->addTest('web_db_connection', [WebTests::class, 'testWebDatabaseConnection'], 'Web数据库连接测试');
    $runner->addTest('web_server_script', [WebTests::class, 'testWebServerScript'], 'Web服务器脚本测试');
    $runner->addTest('web_security', [WebTests::class, 'testWebSecurity'], 'Web安全性测试');
    $runner->addTest('web_responsiveness', [WebTests::class, 'testWebResponsiveness'], 'Web响应性测试');
    $runner->addTest('web_functionality', [WebTests::class, 'testWebFunctionality'], 'Web功能测试');
}

function registerPerformanceTests($runner)
{
    $runner->addTest('db_query_performance', [PerformanceTests::class, 'testDatabaseQueryPerformance'], '数据库查询性能测试');
    $runner->addTest('memory_usage', [PerformanceTests::class, 'testMemoryUsage'], '内存使用测试');
    $runner->addTest('batch_processing', [PerformanceTests::class, 'testBatchProcessingPerformance'], '批量处理性能测试');
    $runner->addTest('concurrency_handling', [PerformanceTests::class, 'testConcurrencyHandling'], '并发处理测试');
    $runner->addTest('file_io_performance', [PerformanceTests::class, 'testFileIOPerformance'], '文件I/O性能测试');
    $runner->addTest('system_resources', [PerformanceTests::class, 'testSystemResources'], '系统资源测试');
}

function runBasicTests()
{
    $runner = new TestRunner();
    registerBasicTests($runner);
    return $runner->runAll();
}

function runApiTests()
{
    $runner = new TestRunner();
    registerApiTests($runner);
    return $runner->runAll();
}

function runWebTests()
{
    $runner = new TestRunner();
    registerWebTests($runner);
    return $runner->runAll();
}

function runPerformanceTests()
{
    $runner = new TestRunner();
    registerPerformanceTests($runner);
    return $runner->runAll();
}

function runQuickCheck()
{
    echo "=== 快速系统检查 ===\n";
    
    $quickTests = [
        '配置文件' => [BasicTests::class, 'testConfiguration'],
        '数据库连接' => [BasicTests::class, 'testDatabaseConnection'],
        '表结构' => [BasicTests::class, 'testTableStructure'],
        'Web文件' => [WebTests::class, 'testWebFilesExist'],
        '系统资源' => [PerformanceTests::class, 'testSystemResources']
    ];
    
    $passed = 0;
    $total = count($quickTests);
    
    foreach ($quickTests as $name => $test) {
        echo "\n检查 {$name}...\n";
        try {
            $result = call_user_func($test);
            if ($result) {
                echo "✅ {$name} 正常\n";
                $passed++;
            } else {
                echo "❌ {$name} 异常\n";
            }
        } catch (Exception $e) {
            echo "❌ {$name} 错误: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== 快速检查结果 ===\n";
    echo "通过: {$passed}/{$total}\n";
    
    if ($passed == $total) {
        echo "🎉 系统状态良好！\n";
        return true;
    } else {
        echo "⚠️  发现问题，建议运行完整测试\n";
        return false;
    }
}

function showTestHistory()
{
    echo "\n=== 测试历史记录 ===\n";
    
    $logFile = __DIR__ . '/logs/test_results.log';
    if (!file_exists($logFile)) {
        echo "暂无测试历史记录\n";
        return;
    }
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $recentLines = array_slice($lines, -10); // 显示最近10条记录
    
    foreach ($recentLines as $line) {
        $data = json_decode(substr($line, 21), true); // 跳过时间戳部分
        if ($data) {
            $timestamp = substr($line, 1, 19);
            echo "{$timestamp} - 通过率: {$data['pass_rate']}% ({$data['passed_tests']}/{$data['total_tests']})\n";
        }
    }
}

function cleanTestData()
{
    echo "\n=== 清理测试数据 ===\n";
    
    try {
        $db = \AlipayBillQuery\Database\Connection::getInstance();
        
        // 清理测试数据
        $testSources = ['test', 'test_suite', 'batch_test', 'dedup_test'];
        $totalCleaned = 0;
        
        foreach ($testSources as $source) {
            $sql = "DELETE FROM `25_zfb_data` WHERE `data_source` = ?";
            $cleaned = $db->update($sql, [$source]);
            $totalCleaned += $cleaned;
            
            if ($cleaned > 0) {
                echo "✅ 清理 {$source} 数据: {$cleaned} 条\n";
            }
        }
        
        // 清理测试日志文件
        $logDir = __DIR__ . '/logs';
        $testLogFiles = glob($logDir . '/test_*.log');
        $logFilesCleaned = 0;
        
        foreach ($testLogFiles as $logFile) {
            if (unlink($logFile)) {
                $logFilesCleaned++;
            }
        }
        
        echo "✅ 清理测试日志文件: {$logFilesCleaned} 个\n";
        echo "✅ 总计清理数据记录: {$totalCleaned} 条\n";
        
        if ($totalCleaned > 0 || $logFilesCleaned > 0) {
            echo "🎉 测试数据清理完成！\n";
        } else {
            echo "ℹ️  没有找到需要清理的测试数据\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 清理测试数据失败: " . $e->getMessage() . "\n";
    }
}

function listAllTests()
{
    $runner = new TestRunner();
    registerBasicTests($runner);
    registerApiTests($runner);
    registerWebTests($runner);
    registerPerformanceTests($runner);
    
    $runner->listTests();
}

// 主程序
if (php_sapi_name() === 'cli') {
    // 命令行模式
    if ($argc > 1) {
        switch ($argv[1]) {
            case 'all':
                exit(runAllTests() ? 0 : 1);
            case 'basic':
                exit(runBasicTests() ? 0 : 1);
            case 'api':
                exit(runApiTests() ? 0 : 1);
            case 'web':
                exit(runWebTests() ? 0 : 1);
            case 'performance':
                exit(runPerformanceTests() ? 0 : 1);
            case 'quick':
                exit(runQuickCheck() ? 0 : 1);
            case 'clean':
                cleanTestData();
                exit(0);
            case 'list':
                listAllTests();
                exit(0);
            default:
                echo "用法: php run_tests.php [all|basic|api|web|performance|quick|clean|list]\n";
                exit(1);
        }
    }
    
    // 交互模式
    while (true) {
        showMenu();
        
        $handle = fopen("php://stdin", "r");
        $choice = trim(fgets($handle));
        fclose($handle);
        
        switch ($choice) {
            case '1':
                runAllTests();
                break;
            case '2':
                runBasicTests();
                break;
            case '3':
                runApiTests();
                break;
            case '4':
                runWebTests();
                break;
            case '5':
                runPerformanceTests();
                break;
            case '6':
                runQuickCheck();
                break;
            case '7':
                showTestHistory();
                break;
            case '8':
                cleanTestData();
                break;
            case '9':
                listAllTests();
                break;
            case '0':
                echo "退出测试程序\n";
                exit(0);
            default:
                echo "无效选择，请重新输入\n";
        }
        
        echo "\n按回车键继续...";
        fgets(fopen("php://stdin", "r"));
    }
} else {
    echo "请在命令行环境下运行此脚本\n";
}
