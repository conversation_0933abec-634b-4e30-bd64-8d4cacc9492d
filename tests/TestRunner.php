<?php
/**
 * 测试运行器
 * 
 * 统一管理和执行所有测试
 * 
 * <AUTHOR>
 * @version 1.0
 */

require_once __DIR__ . '/../vendor/autoload.php';

class TestRunner
{
    private $tests = [];
    private $results = [];
    private $startTime;
    private $logFile;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->logFile = __DIR__ . '/../logs/test_results.log';
        
        // 确保日志目录存在
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * 注册测试
     */
    public function addTest($name, $callable, $description = '')
    {
        $this->tests[$name] = [
            'callable' => $callable,
            'description' => $description ?: $name
        ];
    }

    /**
     * 运行所有测试
     */
    public function runAll()
    {
        $this->printHeader();
        
        foreach ($this->tests as $name => $test) {
            $this->runSingleTest($name, $test);
        }
        
        $this->printSummary();
        $this->saveResults();
        
        return $this->getPassRate() == 100;
    }

    /**
     * 运行单个测试
     */
    public function runSingleTest($name, $test)
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "执行测试: {$test['description']}\n";
        echo str_repeat("=", 60) . "\n";
        
        $startTime = microtime(true);
        $success = false;
        $error = null;
        
        try {
            $success = call_user_func($test['callable']);
        } catch (Exception $e) {
            $error = $e->getMessage();
            echo "❌ 测试异常: " . $error . "\n";
        }
        
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        $this->results[$name] = [
            'success' => $success,
            'duration' => $duration,
            'error' => $error,
            'description' => $test['description']
        ];
        
        $status = $success ? '✅ 通过' : '❌ 失败';
        echo "\n结果: {$status} (耗时: {$duration}ms)\n";
    }

    /**
     * 打印测试头部信息
     */
    private function printHeader()
    {
        echo "支付宝数据同步系统 - 完整测试套件\n";
        echo "================================\n";
        echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
        echo "测试数量: " . count($this->tests) . "\n";
        echo "================================\n";
    }

    /**
     * 打印测试汇总
     */
    private function printSummary()
    {
        $totalTests = count($this->results);
        $passedTests = 0;
        $totalDuration = 0;
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "测试结果汇总\n";
        echo str_repeat("=", 60) . "\n";
        
        foreach ($this->results as $name => $result) {
            $status = $result['success'] ? '✅ 通过' : '❌ 失败';
            echo sprintf("%-30s %s (%sms)\n", 
                $result['description'], 
                $status, 
                $result['duration']
            );
            
            if ($result['success']) {
                $passedTests++;
            }
            
            $totalDuration += $result['duration'];
        }
        
        $endTime = microtime(true);
        $totalTime = round(($endTime - $this->startTime) * 1000, 2);
        
        echo str_repeat("-", 60) . "\n";
        echo "总测试数: {$totalTests}\n";
        echo "通过数: {$passedTests}\n";
        echo "失败数: " . ($totalTests - $passedTests) . "\n";
        echo "通过率: " . $this->getPassRate() . "%\n";
        echo "总耗时: {$totalTime}ms\n";
        
        if ($passedTests == $totalTests) {
            echo "\n🎉 所有测试通过！系统运行正常。\n";
        } else {
            echo "\n⚠️  部分测试失败，请检查相关配置和环境。\n";
        }
    }

    /**
     * 获取通过率
     */
    private function getPassRate()
    {
        $totalTests = count($this->results);
        if ($totalTests == 0) return 0;
        
        $passedTests = array_sum(array_column($this->results, 'success'));
        return round(($passedTests / $totalTests) * 100, 2);
    }

    /**
     * 保存测试结果到日志
     */
    private function saveResults()
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'total_tests' => count($this->results),
            'passed_tests' => array_sum(array_column($this->results, 'success')),
            'pass_rate' => $this->getPassRate(),
            'total_duration' => round((microtime(true) - $this->startTime) * 1000, 2),
            'results' => $this->results
        ];
        
        $logEntry = "[" . date('Y-m-d H:i:s') . "] " . json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * 运行指定的测试
     */
    public function runSpecific($testNames)
    {
        if (!is_array($testNames)) {
            $testNames = [$testNames];
        }
        
        $this->printHeader();
        
        foreach ($testNames as $name) {
            if (isset($this->tests[$name])) {
                $this->runSingleTest($name, $this->tests[$name]);
            } else {
                echo "❌ 测试 '{$name}' 不存在\n";
            }
        }
        
        $this->printSummary();
        $this->saveResults();
    }

    /**
     * 列出所有可用的测试
     */
    public function listTests()
    {
        echo "可用的测试:\n";
        echo str_repeat("-", 40) . "\n";
        
        foreach ($this->tests as $name => $test) {
            echo sprintf("%-20s %s\n", $name, $test['description']);
        }
    }
}
