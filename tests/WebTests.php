<?php
/**
 * Web界面测试
 * 
 * 测试Web管理界面的功能
 * 
 * <AUTHOR>
 * @version 1.0
 */

require_once __DIR__ . '/../vendor/autoload.php';

class WebTests
{
    /**
     * 测试Web文件存在性
     */
    public static function testWebFilesExist()
    {
        echo "检查Web文件存在性...\n";
        
        $webFiles = [
            'web/index.php' => 'Web管理首页',
            'web/data.php' => '数据查看页面',
            'web/README.md' => 'Web说明文档',
            'start_web.php' => 'Web服务启动脚本'
        ];
        
        foreach ($webFiles as $file => $description) {
            $filePath = __DIR__ . '/../' . $file;
            if (!file_exists($filePath)) {
                echo "❌ {$description} 不存在: {$file}\n";
                return false;
            }
            echo "✅ {$description} 存在\n";
        }
        
        return true;
    }

    /**
     * 测试Web文件语法
     */
    public static function testWebFileSyntax()
    {
        echo "检查Web文件PHP语法...\n";
        
        $phpFiles = [
            'web/index.php',
            'web/data.php',
            'start_web.php'
        ];
        
        foreach ($phpFiles as $file) {
            $filePath = __DIR__ . '/../' . $file;
            
            // 使用php -l检查语法
            $output = [];
            $returnCode = 0;
            exec("php -l " . escapeshellarg($filePath) . " 2>&1", $output, $returnCode);
            
            if ($returnCode !== 0) {
                echo "❌ {$file} 语法错误:\n";
                foreach ($output as $line) {
                    echo "   {$line}\n";
                }
                return false;
            }
            
            echo "✅ {$file} 语法正确\n";
        }
        
        return true;
    }

    /**
     * 测试Web页面基本结构
     */
    public static function testWebPageStructure()
    {
        echo "检查Web页面基本结构...\n";
        
        try {
            // 测试index.php
            $indexPath = __DIR__ . '/../web/index.php';
            $indexContent = file_get_contents($indexPath);
            
            $requiredElements = [
                '<!DOCTYPE html>' => 'HTML5文档声明',
                '<html' => 'HTML标签',
                '<head>' => 'Head标签',
                '<title>' => 'Title标签',
                '<body>' => 'Body标签',
                'charset=' => '字符编码'
            ];
            
            foreach ($requiredElements as $element => $description) {
                if (strpos($indexContent, $element) === false) {
                    echo "❌ index.php 缺少 {$description}\n";
                    return false;
                }
            }
            
            echo "✅ index.php 基本结构完整\n";
            
            // 测试data.php
            $dataPath = __DIR__ . '/../web/data.php';
            $dataContent = file_get_contents($dataPath);
            
            if (strpos($dataContent, '<?php') === false) {
                echo "❌ data.php 缺少PHP开始标签\n";
                return false;
            }
            
            echo "✅ data.php 基本结构完整\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Web页面结构检查异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试Web页面数据库连接
     */
    public static function testWebDatabaseConnection()
    {
        echo "测试Web页面数据库连接...\n";
        
        try {
            // 模拟Web环境
            $_SERVER['REQUEST_METHOD'] = 'GET';
            $_SERVER['HTTP_HOST'] = 'localhost';
            $_SERVER['REQUEST_URI'] = '/test';
            
            // 捕获输出
            ob_start();
            
            // 包含并执行data.php的数据库连接部分
            $dataPath = __DIR__ . '/../web/data.php';
            $dataContent = file_get_contents($dataPath);
            
            // 检查是否包含数据库连接代码
            if (strpos($dataContent, 'Connection::getInstance()') !== false ||
                strpos($dataContent, 'require') !== false) {
                echo "✅ Web页面包含数据库连接代码\n";
            } else {
                echo "⚠️  Web页面可能缺少数据库连接代码\n";
            }
            
            ob_end_clean();
            
            return true;
            
        } catch (Exception $e) {
            ob_end_clean();
            echo "❌ Web数据库连接测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试Web服务器启动脚本
     */
    public static function testWebServerScript()
    {
        echo "测试Web服务器启动脚本...\n";
        
        try {
            $startWebPath = __DIR__ . '/../start_web.php';
            $content = file_get_contents($startWebPath);
            
            // 检查必要的功能
            $requiredChecks = [
                'PHP_VERSION' => 'PHP版本检查',
                'is_dir' => '目录存在检查',
                'file_exists' => '文件存在检查'
            ];
            
            foreach ($requiredChecks as $check => $description) {
                if (strpos($content, $check) === false) {
                    echo "❌ 启动脚本缺少 {$description}\n";
                    return false;
                }
            }
            
            echo "✅ Web服务器启动脚本功能完整\n";
            
            // 检查是否有端口配置
            if (strpos($content, '8080') !== false || strpos($content, 'port') !== false) {
                echo "✅ 包含端口配置\n";
            } else {
                echo "⚠️  可能缺少端口配置\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Web服务器脚本测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试Web安全性
     */
    public static function testWebSecurity()
    {
        echo "检查Web安全性...\n";
        
        try {
            $webFiles = ['web/index.php', 'web/data.php'];
            
            foreach ($webFiles as $file) {
                $filePath = __DIR__ . '/../' . $file;
                $content = file_get_contents($filePath);
                
                // 检查是否有基本的安全措施
                $securityChecks = [
                    'htmlspecialchars' => 'HTML转义',
                    'filter_' => '输入过滤',
                    'prepared' => '预处理语句',
                    'escape' => '数据转义'
                ];
                
                $hasSecurityMeasures = false;
                foreach ($securityChecks as $check => $description) {
                    if (strpos($content, $check) !== false) {
                        $hasSecurityMeasures = true;
                        echo "✅ {$file} 包含 {$description}\n";
                        break;
                    }
                }
                
                if (!$hasSecurityMeasures) {
                    echo "⚠️  {$file} 可能缺少安全措施\n";
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Web安全性检查异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试Web响应性
     */
    public static function testWebResponsiveness()
    {
        echo "检查Web响应式设计...\n";
        
        try {
            $webFiles = ['web/index.php'];
            
            foreach ($webFiles as $file) {
                $filePath = __DIR__ . '/../' . $file;
                $content = file_get_contents($filePath);
                
                // 检查响应式设计元素
                $responsiveElements = [
                    'viewport' => '视口设置',
                    'bootstrap' => 'Bootstrap框架',
                    'responsive' => '响应式类',
                    'media' => '媒体查询',
                    'mobile' => '移动端适配'
                ];
                
                $hasResponsive = false;
                foreach ($responsiveElements as $element => $description) {
                    if (stripos($content, $element) !== false) {
                        $hasResponsive = true;
                        echo "✅ {$file} 包含 {$description}\n";
                    }
                }
                
                if (!$hasResponsive) {
                    echo "⚠️  {$file} 可能缺少响应式设计\n";
                } else {
                    echo "✅ {$file} 支持响应式设计\n";
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Web响应性检查异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试Web功能完整性
     */
    public static function testWebFunctionality()
    {
        echo "检查Web功能完整性...\n";
        
        try {
            // 检查index.php功能
            $indexPath = __DIR__ . '/../web/index.php';
            $indexContent = file_get_contents($indexPath);
            
            $indexFeatures = [
                'form' => '表单功能',
                'button' => '按钮交互',
                'table' => '数据表格',
                'script' => 'JavaScript功能'
            ];
            
            foreach ($indexFeatures as $feature => $description) {
                if (stripos($indexContent, $feature) !== false) {
                    echo "✅ index.php 包含 {$description}\n";
                }
            }
            
            // 检查data.php功能
            $dataPath = __DIR__ . '/../web/data.php';
            $dataContent = file_get_contents($dataPath);
            
            $dataFeatures = [
                'SELECT' => 'SQL查询',
                'json' => 'JSON输出',
                'pagination' => '分页功能',
                'search' => '搜索功能'
            ];
            
            foreach ($dataFeatures as $feature => $description) {
                if (stripos($dataContent, $feature) !== false) {
                    echo "✅ data.php 包含 {$description}\n";
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Web功能检查异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
}
