<?php
/**
 * 基础功能测试
 * 
 * 包含数据库连接、表结构、配置等基础测试
 * 
 * <AUTHOR>
 * @version 1.0
 */

require_once __DIR__ . '/../vendor/autoload.php';

use AlipayBillQuery\Database\Connection;
use AlipayBillQuery\Database\Migration;
use AlipayBillQuery\Services\DataService;

class BasicTests
{
    /**
     * 测试配置文件
     */
    public static function testConfiguration()
    {
        echo "检查配置文件...\n";
        
        // 检查数据库配置
        $dbConfigFile = __DIR__ . '/../config/database.php';
        if (!file_exists($dbConfigFile)) {
            echo "❌ 数据库配置文件不存在\n";
            return false;
        }
        
        echo "✅ 数据库配置文件存在\n";
        
        $config = require $dbConfigFile;
        $requiredKeys = ['host', 'username', 'password', 'database', 'charset', 'port'];
        
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                echo "❌ 配置项 {$key} 缺失或为空\n";
                return false;
            }
        }
        
        echo "✅ 数据库配置完整\n";
        
        // 检查必要目录
        $requiredDirs = ['logs', 'vendor', 'src'];
        foreach ($requiredDirs as $dir) {
            $dirPath = __DIR__ . '/../' . $dir;
            if (!is_dir($dirPath)) {
                echo "❌ 目录 {$dir} 不存在\n";
                return false;
            }
            echo "✅ 目录 {$dir} 存在\n";
        }
        
        return true;
    }

    /**
     * 测试数据库连接
     */
    public static function testDatabaseConnection()
    {
        echo "测试数据库连接...\n";
        
        try {
            $db = Connection::getInstance();
            $result = $db->query("SELECT 1 as test, NOW() as server_time")->fetch();

            if ($result['test'] == 1) {
                echo "✅ 数据库连接成功\n";
                echo "   服务器时间: " . $result['server_time'] . "\n";

                // 测试数据库版本
                $version = $db->query("SELECT VERSION() as version")->fetch();
                echo "   MySQL版本: " . $version['version'] . "\n";

                return true;
            } else {
                echo "❌ 数据库连接异常\n";
                return false;
            }
        } catch (Exception $e) {
            echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试表结构
     */
    public static function testTableStructure()
    {
        echo "检查数据库表结构...\n";
        
        try {
            $db = Connection::getInstance();
            $tables = ['25_zfb_data', 'zfb_sync_log'];
            
            foreach ($tables as $table) {
                if (!$db->tableExists($table)) {
                    echo "❌ 表 {$table} 不存在\n";
                    return false;
                }
                
                echo "✅ 表 {$table} 存在\n";
                
                // 获取表结构信息
                $sql = "DESCRIBE `{$table}`";
                $columns = $db->query($sql)->fetchAll();
                echo "   字段数: " . count($columns) . "\n";
                
                // 获取记录数
                $sql = "SELECT COUNT(*) as count FROM `{$table}`";
                $result = $db->query($sql)->fetch();
                echo "   记录数: " . number_format($result['count']) . "\n";
                
                // 检查索引
                $sql = "SHOW INDEX FROM `{$table}`";
                $indexes = $db->query($sql)->fetchAll();
                echo "   索引数: " . count($indexes) . "\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 表结构检查失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试数据操作
     */
    public static function testDataOperations()
    {
        echo "测试数据库操作...\n";
        
        try {
            $db = Connection::getInstance();
            
            // 测试插入操作
            $testData = [
                'alipay_order_no' => 'TEST_' . time() . '_' . rand(1000, 9999),
                'gmt_create' => date('Y-m-d H:i:s'),
                'goods_title' => '测试商品_' . date('His'),
                'total_amount' => 0.01,
                'trade_status' => 'TRADE_SUCCESS',
                'data_source' => 'test_suite'
            ];
            
            $sql = "INSERT INTO `25_zfb_data` 
                    (`alipay_order_no`, `gmt_create`, `goods_title`, `total_amount`, `trade_status`, `data_source`) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $id = $db->insert($sql, [
                $testData['alipay_order_no'],
                $testData['gmt_create'],
                $testData['goods_title'],
                $testData['total_amount'],
                $testData['trade_status'],
                $testData['data_source']
            ]);
            
            if ($id <= 0) {
                echo "❌ 数据插入失败\n";
                return false;
            }
            
            echo "✅ 数据插入成功，ID: {$id}\n";
            
            // 测试查询操作
            $sql = "SELECT * FROM `25_zfb_data` WHERE `id` = ?";
            $result = $db->query($sql, [$id])->fetch();
            
            if (!$result || $result['alipay_order_no'] != $testData['alipay_order_no']) {
                echo "❌ 数据查询失败\n";
                return false;
            }
            
            echo "✅ 数据查询成功\n";
            
            // 测试更新操作
            $newTitle = '测试商品(已更新)_' . date('His');
            $sql = "UPDATE `25_zfb_data` SET `goods_title` = ? WHERE `id` = ?";
            $affectedRows = $db->update($sql, [$newTitle, $id]);
            
            if ($affectedRows <= 0) {
                echo "❌ 数据更新失败\n";
                return false;
            }
            
            echo "✅ 数据更新成功\n";
            
            // 验证更新结果
            $sql = "SELECT `goods_title` FROM `25_zfb_data` WHERE `id` = ?";
            $result = $db->query($sql, [$id])->fetch();
            
            if ($result['goods_title'] != $newTitle) {
                echo "❌ 更新验证失败\n";
                return false;
            }
            
            echo "✅ 更新验证成功\n";
            
            // 清理测试数据
            $sql = "DELETE FROM `25_zfb_data` WHERE `id` = ?";
            $deletedRows = $db->update($sql, [$id]);
            
            if ($deletedRows <= 0) {
                echo "❌ 测试数据清理失败\n";
                return false;
            }
            
            echo "✅ 测试数据清理完成\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 数据操作测试失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试系统服务
     */
    public static function testSystemServices()
    {
        echo "测试系统服务...\n";
        
        try {
            $dataService = new DataService();
            
            // 测试系统状态检查
            echo "检查系统状态...\n";
            $status = $dataService->checkSystemStatus();
            
            if (!$status) {
                echo "❌ 系统服务异常\n";
                return false;
            }
            
            echo "✅ 系统服务正常\n";
            
            // 测试统计信息获取
            echo "获取统计信息...\n";
            $stats = $dataService->getDataStatistics();
            
            if (!is_array($stats)) {
                echo "❌ 统计信息获取失败\n";
                return false;
            }
            
            echo "✅ 统计信息获取成功\n";
            echo "   数据总数: " . ($stats['total_records'] ?? 0) . "\n";
            echo "   最新记录: " . ($stats['latest_record'] ?? '无') . "\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 系统服务测试失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试文件权限
     */
    public static function testFilePermissions()
    {
        echo "检查文件权限...\n";
        
        $checkPaths = [
            'logs' => 'logs',
            'config' => 'config',
            'vendor' => 'vendor'
        ];
        
        foreach ($checkPaths as $name => $path) {
            $fullPath = __DIR__ . '/../' . $path;
            
            if (!is_readable($fullPath)) {
                echo "❌ {$name} 目录不可读: {$path}\n";
                return false;
            }
            
            echo "✅ {$name} 目录可读\n";
            
            if ($name === 'logs' && !is_writable($fullPath)) {
                echo "❌ {$name} 目录不可写: {$path}\n";
                return false;
            }
            
            if ($name === 'logs') {
                echo "✅ {$name} 目录可写\n";
            }
        }
        
        return true;
    }
}
