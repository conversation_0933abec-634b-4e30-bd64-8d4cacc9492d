<?php

namespace AlipayBillQuery\Database;

use Exception;

/**
 * 数据库迁移类
 */
class Migration
{
    private $db;

    public function __construct()
    {
        $this->db = Connection::getInstance();
    }

    /**
     * 创建25_zfb_data表
     */
    public function createZfbDataTable()
    {
        $sql = "
        CREATE TABLE IF NOT EXISTS `25_zfb_data` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `alipay_order_no` varchar(64) NOT NULL COMMENT '支付宝订单号',
            `merchant_order_no` varchar(64) DEFAULT NULL COMMENT '商户订单号',
            `gmt_create` datetime NOT NULL COMMENT '交易创建时间',
            `gmt_payment` datetime DEFAULT NULL COMMENT '交易付款时间',
            `goods_title` varchar(255) DEFAULT NULL COMMENT '商品标题',
            `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '交易金额',
            `other_account` varchar(128) DEFAULT NULL COMMENT '对方账户',
            `trade_status` varchar(32) DEFAULT NULL COMMENT '交易状态',
            `trade_type` varchar(32) DEFAULT NULL COMMENT '交易类型',
            `store_name` varchar(128) DEFAULT NULL COMMENT '店铺名称',
            `buyer_user_id` varchar(32) DEFAULT NULL COMMENT '买家用户ID',
            `seller_user_id` varchar(32) DEFAULT NULL COMMENT '卖家用户ID',
            `trade_no` varchar(64) DEFAULT NULL COMMENT '交易流水号',
            `subject` varchar(255) DEFAULT NULL COMMENT '订单标题',
            `body` text DEFAULT NULL COMMENT '订单描述',
            `receipt_amount` decimal(10,2) DEFAULT NULL COMMENT '实收金额',
            `buyer_pay_amount` decimal(10,2) DEFAULT NULL COMMENT '买家付款金额',
            `point_amount` decimal(10,2) DEFAULT NULL COMMENT '积分金额',
            `invoice_amount` decimal(10,2) DEFAULT NULL COMMENT '开票金额',
            `send_pay_date` datetime DEFAULT NULL COMMENT '打款给卖家时间',
            `alipay_store_id` varchar(32) DEFAULT NULL COMMENT '支付宝店铺编号',
            `store_id` varchar(32) DEFAULT NULL COMMENT '商户门店编号',
            `terminal_id` varchar(32) DEFAULT NULL COMMENT '商户机具终端编号',
            `fund_bill_list` text DEFAULT NULL COMMENT '交易资金渠道',
            `voucher_detail_list` text DEFAULT NULL COMMENT '优惠券信息',
            `raw_data` json DEFAULT NULL COMMENT '原始数据JSON',
            `data_source` varchar(32) DEFAULT 'api' COMMENT '数据来源',
            `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_alipay_order_no` (`alipay_order_no`),
            KEY `idx_gmt_create` (`gmt_create`),
            KEY `idx_trade_status` (`trade_status`),
            KEY `idx_total_amount` (`total_amount`),
            KEY `idx_last_updated` (`last_updated`),
            KEY `idx_merchant_order_no` (`merchant_order_no`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付宝交易数据表';
        ";

        try {
            $this->db->query($sql);
            echo "✅ 表 25_zfb_data 创建成功\n";
            return true;
        } catch (Exception $e) {
            echo "❌ 表创建失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 创建数据同步日志表
     */
    public function createSyncLogTable()
    {
        $sql = "
        CREATE TABLE IF NOT EXISTS `zfb_sync_log` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `sync_type` varchar(32) NOT NULL COMMENT '同步类型: initial, incremental',
            `start_time` datetime NOT NULL COMMENT '同步开始时间',
            `end_time` datetime NOT NULL COMMENT '同步结束时间',
            `total_records` int(11) DEFAULT 0 COMMENT '总记录数',
            `new_records` int(11) DEFAULT 0 COMMENT '新增记录数',
            `updated_records` int(11) DEFAULT 0 COMMENT '更新记录数',
            `error_records` int(11) DEFAULT 0 COMMENT '错误记录数',
            `status` varchar(16) DEFAULT 'running' COMMENT '同步状态: running, success, failed',
            `error_message` text DEFAULT NULL COMMENT '错误信息',
            `execution_time` int(11) DEFAULT 0 COMMENT '执行时间(秒)',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`),
            KEY `idx_sync_type` (`sync_type`),
            KEY `idx_start_time` (`start_time`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步日志表';
        ";

        try {
            $this->db->query($sql);
            echo "✅ 表 zfb_sync_log 创建成功\n";
            return true;
        } catch (Exception $e) {
            echo "❌ 同步日志表创建失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 运行所有迁移
     */
    public function runMigrations()
    {
        echo "开始执行数据库迁移...\n";
        echo "================================\n";
        
        $success = true;
        
        // 创建主数据表
        if (!$this->createZfbDataTable()) {
            $success = false;
        }
        
        // 创建同步日志表
        if (!$this->createSyncLogTable()) {
            $success = false;
        }
        
        echo "================================\n";
        if ($success) {
            echo "✅ 所有数据库迁移执行成功！\n";
        } else {
            echo "❌ 部分迁移执行失败，请检查错误信息\n";
        }
        
        return $success;
    }

    /**
     * 检查表结构
     */
    public function checkTables()
    {
        $tables = ['25_zfb_data', 'zfb_sync_log'];
        
        echo "检查数据库表结构...\n";
        echo "================================\n";
        
        foreach ($tables as $table) {
            if ($this->db->tableExists($table)) {
                echo "✅ 表 {$table} 存在\n";
                
                // 获取表信息
                $sql = "SELECT COUNT(*) as count FROM `{$table}`";
                $result = $this->db->query($sql)->fetch();
                echo "   记录数: {$result['count']}\n";
            } else {
                echo "❌ 表 {$table} 不存在\n";
            }
        }
        
        echo "================================\n";
    }
}
