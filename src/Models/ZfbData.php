<?php

namespace AlipayBillQuery\Models;

use AlipayBillQuery\Database\Connection;
use Exception;

/**
 * 支付宝数据模型类
 */
class ZfbData
{
    private $db;
    private $table = '25_zfb_data';

    public function __construct()
    {
        $this->db = Connection::getInstance();
    }

    /**
     * 插入或更新数据
     */
    public function insertOrUpdate($data)
    {
        try {
            // 检查是否已存在
            $existing = $this->findByOrderNo($data['alipay_order_no']);
            
            if ($existing) {
                // 检查是否需要更新（超过2个月的数据不更新）
                $createTime = new \DateTime($existing['gmt_create']);
                $twoMonthsAgo = new \DateTime('-2 months');
                
                if ($createTime < $twoMonthsAgo) {
                    return ['action' => 'skipped', 'reason' => 'too_old', 'id' => $existing['id']];
                }
                
                // 更新现有记录
                return $this->updateRecord($existing['id'], $data);
            } else {
                // 插入新记录
                return $this->insertRecord($data);
            }
        } catch (Exception $e) {
            throw new Exception("数据操作失败: " . $e->getMessage());
        }
    }

    /**
     * 根据支付宝订单号查找记录
     */
    public function findByOrderNo($alipayOrderNo)
    {
        $sql = "SELECT * FROM `{$this->table}` WHERE `alipay_order_no` = ? LIMIT 1";
        $stmt = $this->db->query($sql, [$alipayOrderNo]);
        return $stmt->fetch();
    }

    /**
     * 插入新记录
     */
    private function insertRecord($data)
    {
        $fields = [
            'alipay_order_no', 'merchant_order_no', 'gmt_create', 'gmt_payment',
            'goods_title', 'total_amount', 'other_account', 'trade_status',
            'trade_type', 'store_name', 'buyer_user_id', 'seller_user_id',
            'trade_no', 'subject', 'body', 'receipt_amount', 'buyer_pay_amount',
            'point_amount', 'invoice_amount', 'send_pay_date', 'alipay_store_id',
            'store_id', 'terminal_id', 'fund_bill_list', 'voucher_detail_list',
            'raw_data', 'data_source'
        ];

        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        $sql = "INSERT INTO `{$this->table}` (`" . implode('`, `', $fields) . "`) VALUES ({$placeholders})";

        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }

        $id = $this->db->insert($sql, $values);
        return ['action' => 'inserted', 'id' => $id];
    }

    /**
     * 更新记录
     */
    private function updateRecord($id, $data)
    {
        $fields = [
            'merchant_order_no', 'gmt_payment', 'goods_title', 'total_amount',
            'other_account', 'trade_status', 'trade_type', 'store_name',
            'buyer_user_id', 'seller_user_id', 'trade_no', 'subject', 'body',
            'receipt_amount', 'buyer_pay_amount', 'point_amount', 'invoice_amount',
            'send_pay_date', 'alipay_store_id', 'store_id', 'terminal_id',
            'fund_bill_list', 'voucher_detail_list', 'raw_data'
        ];

        $setClause = implode(' = ?, ', $fields) . ' = ?';
        $sql = "UPDATE `{$this->table}` SET {$setClause} WHERE `id` = ?";

        $values = [];
        foreach ($fields as $field) {
            $values[] = $data[$field] ?? null;
        }
        $values[] = $id;

        $affectedRows = $this->db->update($sql, $values);
        return ['action' => 'updated', 'id' => $id, 'affected_rows' => $affectedRows];
    }

    /**
     * 批量插入数据
     */
    public function batchInsert($dataList)
    {
        $results = [
            'inserted' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'details' => []
        ];

        $this->db->beginTransaction();
        
        try {
            foreach ($dataList as $data) {
                try {
                    $result = $this->insertOrUpdate($data);
                    $results[$result['action']]++;
                    $results['details'][] = $result;
                } catch (Exception $e) {
                    $results['errors']++;
                    $results['details'][] = [
                        'action' => 'error',
                        'error' => $e->getMessage(),
                        'data' => $data['alipay_order_no'] ?? 'unknown'
                    ];
                }
            }
            
            $this->db->commit();
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }

        return $results;
    }

    /**
     * 获取统计信息
     */
    public function getStatistics()
    {
        $stats = [];

        // 总记录数
        $sql = "SELECT COUNT(*) as total FROM `{$this->table}`";
        $result = $this->db->query($sql)->fetch();
        $stats['total_records'] = $result['total'];

        // 今日新增
        $sql = "SELECT COUNT(*) as today FROM `{$this->table}` WHERE DATE(created_at) = CURDATE()";
        $result = $this->db->query($sql)->fetch();
        $stats['today_records'] = $result['today'];

        // 成功交易统计
        $sql = "SELECT COUNT(*) as success_count, SUM(total_amount) as success_amount 
                FROM `{$this->table}` WHERE trade_status = '成功'";
        $result = $this->db->query($sql)->fetch();
        $stats['success_count'] = $result['success_count'];
        $stats['success_amount'] = $result['success_amount'] ?? 0;

        // 最新记录时间
        $sql = "SELECT MAX(gmt_create) as latest_time FROM `{$this->table}`";
        $result = $this->db->query($sql)->fetch();
        $stats['latest_time'] = $result['latest_time'];

        // 最早记录时间
        $sql = "SELECT MIN(gmt_create) as earliest_time FROM `{$this->table}`";
        $result = $this->db->query($sql)->fetch();
        $stats['earliest_time'] = $result['earliest_time'];

        return $stats;
    }

    /**
     * 获取指定时间范围的数据
     */
    public function getDataByTimeRange($startTime, $endTime, $limit = 1000, $offset = 0)
    {
        $sql = "SELECT * FROM `{$this->table}` 
                WHERE gmt_create >= ? AND gmt_create <= ? 
                ORDER BY gmt_create DESC 
                LIMIT ? OFFSET ?";
        
        $stmt = $this->db->query($sql, [$startTime, $endTime, $limit, $offset]);
        return $stmt->fetchAll();
    }

    /**
     * 清理旧数据（可选功能）
     */
    public function cleanOldData($monthsToKeep = 12)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$monthsToKeep} months"));
        
        $sql = "DELETE FROM `{$this->table}` WHERE gmt_create < ?";
        $affectedRows = $this->db->update($sql, [$cutoffDate]);
        
        return $affectedRows;
    }
}
