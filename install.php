<?php
/**
 * 安装脚本
 *
 * 用于初始化数据库表和执行初始数据同步
 */

// 设置时区
require_once 'config/timezone.php';

require_once 'vendor/autoload.php';

use AlipayBillQuery\Database\Migration;
use AlipayBillQuery\Services\Scheduler;
use AlipayBillQuery\Services\DataService;

// 支付宝配置信息
$appId = '2021005119698187';
$privateKey = 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCIAuPG6ks9kh07vX9fHTH/+6jNG1glO5/mqIXQKO/g3Dvu+eYFbket9vZbVAO0TQYd/p2Lzp2WZBmfxocHYDumM/cLvVAEW8sDKkTLOWgV06yvucnbalTltHus43KTcx1KvmybN2wJRxOsaRWAEk+awVkibhpKGliNr4b1ah8XYWCrePGAUUXMRj04vXufhGnSe+E6ryUGwul9ZjdYtLx4mznEHLNvPc7Vojv2R514Jwp1b/q3IqPSIArpaGSeUs79i/R1blC1LrSViniVqnLpp0kRkEuS0/hjDPImEcie0QwjWWfHKQqP30dTTh+gSh7v0Iv+yfOOi/PvcBncKZx1AgMBAAECggEALFAEtgIPkXfRXm1W2j5A1A3B6VFHXpoWdqfhMPilbrVSMYHpl0tevyb/DLJKoquVmqAh5DLk1OK4Fn4v8A9CX9v+WSzMrR7a/aT/1NZXOwVD9dyqD3qNPmmXAbT412Fh4cA40jk0UbF+j2WNQ7SzitADolwM5KfAwii1568zggISOoQdkvR9+ERdW0NbY+6nyQj5fPJeQb+PaSU4XUL+YvH38PBFQ21LSdxC4hpR+btYxu8KJ0a5lhXu8FPVEjshYMhUwwGUUXbt/7mer2Yg6X3zN2q6DUXvQXZAtLSUlzQvxDgIyUJAt61v+T3/xaZpRVLRFaSGsNOXUmRluZAQ4QKBgQDxkfJBYMnzg86WMZqFrToB4PwL8RgKFNUkL9LCHLVxK7kfksuYVJsr8BWvc8rrQuCjhjdBhylddUqeGDJ+8I36ti9UZ4vDgb0S1pMYvQRmUZeC37EKTKUjSsu5hICd8mgY3AuRUNJkFAkuFQMSeHSWe0dz2UyBVoM+oYJ1T8k3UwKBgQCQIsEA/dZYXVlfGGSVXhNmy8uTnCC010pQ2jq3rKiK0kxXvGSAooSIuML46hwn2TpJXW5sYPnvmMl4osEBtaGuP8OSlgJ/7ZP56LHCJeY2nBqOZzngyFdZwQBCkig4Dm1HZF53lfRQCks3YHauWMXpYa/dDn+yOgVZcOSgEJNMFwKBgQCDnr2cGZxvbhWViBllVGkStP8fkpFCjO9E9DmlQfcqXmRTa6w6p36Uhg+KtVCOtrWm424f6gEDxvCNCyoYOAFj5PgMyQ5By+K07Ozgwbwv86zVxgO0VOZ1QD+YKTXa2UUWpm43Ew5PMQt/bDtsSO1dQHZCDNe+cOC5s05dlMdRuQKBgQCIsOzoy9IjKyQ+kxuQrA8qRctiyYYa+rF3y/4zgoK0ZIwSCJAnjfiy0MXW2e6pu9ETEpBOKAnft74Zsf/oZyBV6BLJSYpFWEIllxA9V0PkNlbZBfxVuKlebTKZ75JE1ym7suwD7SotXhXHBqyG25mVoxbtRXrEw1GfaPjo8889MQKBgQDeTTXt6cv4PRB9VN981cqmJJUJVMqpI8CDjpzeZE95G3aljeQcfWBSpD1koudYMaTeWxXWLqt4a+Yz8D4/AAf5tZeAYpm7jksaihR4NVjG9w66JKcCXYa69e5yDIx5+Wlq6QAUWQOyFdttbYYIotGmAmJtHlkkZ+VnXRodRsz/Bg==';
$alipayPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq+s69Posg9bkvSDge58uVtNaSY0msXaAe4LyJTfDp1mvQKlpeRq01ic+yLnGNoEByosOqC4PG+xMzcahU/+1QD4Lnt5Y9p1uzMozGmE2pE2TZcIoaquW75ylxSYURGYHAJ2X5Xk9y1hVKJxLVeDCEY3HfCa+ymlWguSB8DYWE7mJyFtXWrSZOVzfiV6+m1FKVrfWsFBwf+Din8OKBes1AGMZ2xsVoCl+m4Mp9d1j8cbH0OwKTwNUJsOJLEKAxmy6Nkhl+6/fJu7tIAvQn76fi/oTYu71XWAfpoex1TehETBZ/6bNqSr3ztZLZF1fGGpcTr6gWan1Vye6VTzRApnvDwIDAQAB';

function showMenu()
{
    echo "\n";
    echo "=== 支付宝数据同步系统安装程序 ===\n";
    echo "1. 创建数据库表\n";
    echo "2. 检查数据库表状态\n";
    echo "3. 执行初始数据同步\n";
    echo "4. 安装定时任务\n";
    echo "5. 检查定时任务状态\n";
    echo "6. 卸载定时任务\n";
    echo "7. 查看数据统计\n";
    echo "8. 查看同步日志\n";
    echo "9. 检查系统状态\n";
    echo "0. 退出\n";
    echo "================================\n";
    echo "请选择操作: ";
}

function createTables()
{
    echo "\n=== 创建数据库表 ===\n";
    try {
        $migration = new Migration();
        $migration->runMigrations();
    } catch (Exception $e) {
        echo "❌ 创建表失败: " . $e->getMessage() . "\n";
    }
}

function checkTables()
{
    echo "\n=== 检查数据库表状态 ===\n";
    try {
        $migration = new Migration();
        $migration->checkTables();
    } catch (Exception $e) {
        echo "❌ 检查表状态失败: " . $e->getMessage() . "\n";
    }
}

function runInitialSync($appId, $privateKey, $alipayPublicKey)
{
    echo "\n=== 执行初始数据同步 ===\n";
    echo "⚠️  注意: 初始同步可能需要较长时间，请耐心等待\n";
    echo "是否继续? (y/N): ";
    
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) !== 'y') {
        echo "取消初始同步\n";
        return;
    }
    
    try {
        $scheduler = new Scheduler($appId, $privateKey, $alipayPublicKey);
        $scheduler->runInitialSync('2024-01-01', '2025-05-31');
    } catch (Exception $e) {
        echo "❌ 初始同步失败: " . $e->getMessage() . "\n";
    }
}

function installCrontab()
{
    echo "\n=== 安装定时任务 ===\n";
    $projectPath = realpath(__DIR__);
    Scheduler::installCrontab($projectPath);
}

function checkCrontab()
{
    echo "\n=== 检查定时任务状态 ===\n";
    Scheduler::checkCrontabStatus();
}

function uninstallCrontab()
{
    echo "\n=== 卸载定时任务 ===\n";
    echo "确定要卸载定时任务吗? (y/N): ";
    
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    fclose($handle);
    
    if (trim(strtolower($line)) === 'y') {
        Scheduler::uninstallCrontab();
    } else {
        echo "取消卸载\n";
    }
}

function showStatistics($appId, $privateKey, $alipayPublicKey)
{
    echo "\n=== 数据统计信息 ===\n";
    try {
        $scheduler = new Scheduler($appId, $privateKey, $alipayPublicKey);
        $scheduler->getStatistics();
    } catch (Exception $e) {
        echo "❌ 获取统计信息失败: " . $e->getMessage() . "\n";
    }
}

function showSyncLogs($appId, $privateKey, $alipayPublicKey)
{
    echo "\n=== 同步日志 ===\n";
    try {
        $scheduler = new Scheduler($appId, $privateKey, $alipayPublicKey);
        $scheduler->getSyncLogs(20);
    } catch (Exception $e) {
        echo "❌ 获取同步日志失败: " . $e->getMessage() . "\n";
    }
}

function checkSystemStatus()
{
    echo "\n=== 系统状态检查 ===\n";
    try {
        $dataService = new DataService();
        $dataService->checkSystemStatus();
    } catch (Exception $e) {
        echo "❌ 系统状态检查失败: " . $e->getMessage() . "\n";
    }
}

// 主程序
echo "支付宝数据同步系统安装程序\n";
echo "版本: 1.0\n";

while (true) {
    showMenu();
    
    $handle = fopen("php://stdin", "r");
    $choice = trim(fgets($handle));
    fclose($handle);
    
    switch ($choice) {
        case '1':
            createTables();
            break;
        case '2':
            checkTables();
            break;
        case '3':
            runInitialSync($appId, $privateKey, $alipayPublicKey);
            break;
        case '4':
            installCrontab();
            break;
        case '5':
            checkCrontab();
            break;
        case '6':
            uninstallCrontab();
            break;
        case '7':
            showStatistics($appId, $privateKey, $alipayPublicKey);
            break;
        case '8':
            showSyncLogs($appId, $privateKey, $alipayPublicKey);
            break;
        case '9':
            checkSystemStatus();
            break;
        case '0':
            echo "退出安装程序\n";
            exit(0);
        default:
            echo "无效选择，请重新输入\n";
    }
    
    echo "\n按回车键继续...";
    $handle = fopen("php://stdin", "r");
    fgets($handle);
    fclose($handle);
}
