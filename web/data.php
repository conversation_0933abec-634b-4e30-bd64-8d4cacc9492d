<?php
/**
 * 支付宝数据查看页面
 *
 * @version 1.0
 */

// 设置时区
require_once '../config/timezone.php';

require_once '../vendor/autoload.php';

use AlipayBillQuery\Database\Connection;

// 分页参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// 搜索参数
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';

try {
    $db = Connection::getInstance();
    
    // 构建查询条件
    $where = ['1=1'];
    $params = [];
    
    if (!empty($search)) {
        $where[] = "(goods_title LIKE ? OR alipay_order_no LIKE ? OR other_account LIKE ?)";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    if (!empty($status)) {
        $where[] = "trade_status = ?";
        $params[] = $status;
    }
    
    if (!empty($date_from)) {
        $where[] = "gmt_create >= ?";
        $params[] = $date_from . ' 00:00:00';
    }
    
    if (!empty($date_to)) {
        $where[] = "gmt_create <= ?";
        $params[] = $date_to . ' 23:59:59';
    }
    
    $whereClause = implode(' AND ', $where);
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM `25_zfb_data` WHERE {$whereClause}";
    $totalResult = $db->query($countSql, $params)->fetch();
    $total = $totalResult['total'];
    $totalPages = ceil($total / $limit);
    
    // 获取数据
    $sql = "SELECT * FROM `25_zfb_data` WHERE {$whereClause} ORDER BY gmt_create DESC LIMIT {$limit} OFFSET {$offset}";
    $stmt = $db->query($sql, $params);
    $data = $stmt->fetchAll();
    
    // 获取交易状态列表
    $statusSql = "SELECT DISTINCT trade_status FROM `25_zfb_data` WHERE trade_status IS NOT NULL ORDER BY trade_status";
    $statusList = $db->query($statusSql)->fetchAll();
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据查看 - 支付宝数据同步系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .filters {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .stats {
            padding: 15px 20px;
            background: #e3f2fd;
            color: #1565c0;
            font-weight: 500;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .pagination {
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
        }
        
        .pagination a,
        .pagination span {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination a:hover {
            background: #e9ecef;
        }
        
        .pagination .current {
            background: #007bff;
            color: white;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 20px;
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
            }
            
            .nav-links {
                margin-top: 10px;
            }
            
            .nav-links a {
                margin: 0 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 数据查看</h1>
            <div class="nav-links">
                <a href="index.php">🏠 管理首页</a>
                <a href="data.php">📊 数据查看</a>
            </div>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="error">❌ 错误: <?= htmlspecialchars($error) ?></div>
        <?php else: ?>
            
            <div class="filters">
                <form method="GET" class="filter-row">
                    <div class="form-group">
                        <label>搜索关键词</label>
                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                               placeholder="商品标题、订单号、账户...">
                    </div>
                    
                    <div class="form-group">
                        <label>交易状态</label>
                        <select name="status">
                            <option value="">全部状态</option>
                            <?php foreach ($statusList as $s): ?>
                                <option value="<?= htmlspecialchars($s['trade_status']) ?>" 
                                        <?= $status === $s['trade_status'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($s['trade_status']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>开始日期</label>
                        <input type="date" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                    </div>
                    
                    <div class="form-group">
                        <label>结束日期</label>
                        <input type="date" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn">🔍 搜索</button>
                    </div>
                </form>
            </div>
            
            <div class="stats">
                📈 共找到 <?= number_format($total) ?> 条记录，当前第 <?= $page ?>/<?= $totalPages ?> 页
            </div>
            
            <?php if (empty($data)): ?>
                <div class="no-data">
                    <h3>😔 暂无数据</h3>
                    <p>请尝试调整搜索条件</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>创建时间</th>
                                <th>商品标题</th>
                                <th>交易金额</th>
                                <th>对方账户</th>
                                <th>交易状态</th>
                                <th>支付宝订单号</th>
                                <th>商户订单号</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data as $row): ?>
                                <tr>
                                    <td><?= htmlspecialchars($row['gmt_create']) ?></td>
                                    <td><?= htmlspecialchars($row['goods_title'] ?: '-') ?></td>
                                    <td class="amount">¥<?= number_format($row['total_amount'], 2) ?></td>
                                    <td><?= htmlspecialchars($row['other_account'] ?: '-') ?></td>
                                    <td>
                                        <span class="status <?= $row['trade_status'] === '成功' ? 'status-success' : 
                                            ($row['trade_status'] === '失败' ? 'status-failed' : 'status-pending') ?>">
                                            <?= htmlspecialchars($row['trade_status'] ?: '-') ?>
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($row['alipay_order_no']) ?></td>
                                    <td><?= htmlspecialchars($row['merchant_order_no'] ?: '-') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php
                        $currentUrl = $_SERVER['REQUEST_URI'];
                        $urlParts = parse_url($currentUrl);
                        parse_str($urlParts['query'] ?? '', $queryParams);
                        
                        // 上一页
                        if ($page > 1):
                            $queryParams['page'] = $page - 1;
                            $prevUrl = '?' . http_build_query($queryParams);
                        ?>
                            <a href="<?= $prevUrl ?>">« 上一页</a>
                        <?php endif; ?>
                        
                        <?php
                        // 页码
                        $start = max(1, $page - 2);
                        $end = min($totalPages, $page + 2);
                        
                        for ($i = $start; $i <= $end; $i++):
                            $queryParams['page'] = $i;
                            $pageUrl = '?' . http_build_query($queryParams);
                        ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?= $i ?></span>
                            <?php else: ?>
                                <a href="<?= $pageUrl ?>"><?= $i ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php
                        // 下一页
                        if ($page < $totalPages):
                            $queryParams['page'] = $page + 1;
                            $nextUrl = '?' . http_build_query($queryParams);
                        ?>
                            <a href="<?= $nextUrl ?>">下一页 »</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
