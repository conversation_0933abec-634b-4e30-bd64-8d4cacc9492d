<?php
/**
 * 支付宝数据同步系统 Web 管理界面 - 合并版本
 * 包含管理首页和数据查看功能
 *
 * @version 2.0
 */

// 设置时区
require_once '../config/timezone.php';

require_once '../vendor/autoload.php';

use AlipayBillQuery\Services\DataService;
use AlipayBillQuery\Services\Scheduler;
use AlipayBillQuery\Database\Migration;
use AlipayBillQuery\Database\Connection;

// 支付宝配置信息
$appId = '2021005119698187';
$privateKey = 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCIAuPG6ks9kh07vX9fHTH/+6jNG1glO5/mqIXQKO/g3Dvu+eYFbket9vZbVAO0TQYd/p2Lzp2WZBmfxocHYDumM/cLvVAEW8sDKkTLOWgV06yvucnbalTltHus43KTcx1KvmybN2wJRxOsaRWAEk+awVkibhpKGliNr4b1ah8XYWCrePGAUUXMRj04vXufhGnSe+E6ryUGwul9ZjdYtLx4mznEHLNvPc7Vojv2R514Jwp1b/q3IqPSIArpaGSeUs79i/R1blC1LrSViniVqnLpp0kRkEuS0/hjDPImEcie0QwjWWfHKQqP30dTTh+gSh7v0Iv+yfOOi/PvcBncKZx1AgMBAAECggEALFAEtgIPkXfRXm1W2j5A1A3B6VFHXpoWdqfhMPilbrVSMYHpl0tevyb/DLJKoquVmqAh5DLk1OK4Fn4v8A9CX9v+WSzMrR7a/aT/1NZXOwVD9dyqD3qNPmmXAbT412Fh4cA40jk0UbF+j2WNQ7SzitADolwM5KfAwii1568zggISOoQdkvR9+ERdW0NbY+6nyQj5fPJeQb+PaSU4XUL+YvH38PBFQ21LSdxC4hpR+btYxu8KJ0a5lhXu8FPVEjshYMhUwwGUUXbt/7mer2Yg6X3zN2q6DUXvQXZAtLSUlzQvxDgIyUJAt61v+T3/xaZpRVLRFaSGsNOXUmRluZAQ4QKBgQDxkfJBYMnzg86WMZqFrToB4PwL8RgKFNUkL9LCHLVxK7kfksuYVJsr8BWvc8rrQuCjhjdBhylddUqeGDJ+8I36ti9UZ4vDgb0S1pMYvQRmUZeC37EKTKUjSsu5hICd8mgY3AuRUNJkFAkuFQMSeHSWe0dz2UyBVoM+oYJ1T8k3UwKBgQCQIsEA/dZYXVlfGGSVXhNmy8uTnCC010pQ2jq3rKiK0kxXvGSAooSIuML46hwn2TpJXW5sYPnvmMl4osEBtaGuP8OSlgJ/7ZP56LHCJeY2nBqOZzngyFdZwQBCkig4Dm1HZF53lfRQCks3YHauWMXpYa/dDn+yOgVZcOSgEJNMFwKBgQCDnr2cGZxvbhWViBllVGkStP8fkpFCjO9E9DmlQfcqXmRTa6w6p36Uhg+KtVCOtrWm424f6gEDxvCNCyoYOAFj5PgMyQ5By+K07Ozgwbwv86zVxgO0VOZ1QD+YKTXa2UUWpm43Ew5PMQt/bDtsSO1dQHZCDNe+cOC5s05dlMdRuQKBgQCIsOzoy9IjKyQ+kxuQrA8qRctiyYYa+rF3y/4zgoK0ZIwSCJAnjfiy0MXW2e6pu9ETEpBOKAnft74Zsf/oZyBV6BLJSYpFWEIllxA9V0PkNlbZBfxVuKlebTKZ75JE1ym7suwD7SotXhXHBqyG25mVoxbtRXrEw1GfaPjo8889MQKBgQDeTTXt6cv4PRB9VN981cqmJJUJVMqpI8CDjpzeZE95G3aljeQcfWBSpD1koudYMaTeWxXWLqt4a+Yz8D4/AAf5tZeAYpm7jksaihR4NVjG9w66JKcCXYa69e5yDIx5+Wlq6QAUWQOyFdttbYYIotGmAmJtHlkkZ+VnXRodRsz/Bg==';
$alipayPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq+s69Posg9bkvSDge58uVtNaSY0msXaAe4LyJTfDp1mvQKlpeRq01ic+yLnGNoEByosOqC4PG+xMzcahU/+1QD4Lnt5Y9p1uzMozGmE2pE2TZcIoaquW75ylxSYURGYHAJ2X5Xk9y1hVKJxLVeDCEY3HfCa+ymlWguSB8DYWE7mJyFtXWrSZOVzfiV6+m1FKVrfWsFBwf+Din8OKBes1AGMZ2xsVoCl+m4Mp9d1j8cbH0OwKTwNUJsOJLEKAxmy6Nkhl+6/fJu7tIAvQn76fi/oTYu71XWAfpoex1TehETBZ/6bNqSr3ztZLZF1fGGpcTr6gWan1Vye6VTzRApnvDwIDAQAB';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        $response = ['success' => false, 'message' => '', 'data' => null];
        
        switch ($action) {
            case 'get_statistics':
                $dataService = new DataService();
                $stats = $dataService->getDataStatistics(true);
                $response = ['success' => true, 'data' => $stats];
                break;
                
            case 'get_sync_logs':
                $dataService = new DataService();
                $logs = $dataService->getSyncLogs(20, true);
                $response = ['success' => true, 'data' => $logs];
                break;

            case 'manual_sync':
                $dataService = new DataService();
                $success = $dataService->performIncrementalSync($appId, $privateKey, $alipayPublicKey, 60, true);
                $response = ['success' => $success, 'message' => $success ? '智能同步成功' : '同步失败'];
                break;

            case 'check_system':
                $dataService = new DataService();
                $status = $dataService->checkSystemStatus(true);
                $response = ['success' => $status, 'message' => $status ? '系统正常' : '系统异常'];
                break;
                
            case 'create_tables':
                $migration = new Migration();
                $success = $migration->runMigrations();
                $response = ['success' => $success, 'message' => $success ? '表创建成功' : '表创建失败'];
                break;

            case 'get_data':
                // 数据查看功能
                $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
                $limit = 20;
                $offset = ($page - 1) * $limit;

                $search = isset($_POST['search']) ? trim($_POST['search']) : '';
                $status = isset($_POST['status']) ? trim($_POST['status']) : '';
                $date_from = isset($_POST['date_from']) ? trim($_POST['date_from']) : '';
                $date_to = isset($_POST['date_to']) ? trim($_POST['date_to']) : '';

                try {
                    $db = Connection::getInstance();

                    // 构建查询条件
                    $where = ['1=1'];
                    $params = [];

                    if (!empty($search)) {
                        $where[] = "(goods_title LIKE ? OR alipay_order_no LIKE ? OR other_account LIKE ?)";
                        $params[] = "%{$search}%";
                        $params[] = "%{$search}%";
                        $params[] = "%{$search}%";
                    }

                    if (!empty($status)) {
                        $where[] = "trade_status = ?";
                        $params[] = $status;
                    }

                    if (!empty($date_from)) {
                        $where[] = "gmt_create >= ?";
                        $params[] = $date_from . ' 00:00:00';
                    }

                    if (!empty($date_to)) {
                        $where[] = "gmt_create <= ?";
                        $params[] = $date_to . ' 23:59:59';
                    }

                    $whereClause = implode(' AND ', $where);

                    // 获取总记录数
                    $countSql = "SELECT COUNT(*) as total FROM `25_zfb_data` WHERE {$whereClause}";
                    $totalResult = $db->query($countSql, $params)->fetch();
                    $total = $totalResult['total'];
                    $totalPages = ceil($total / $limit);

                    // 获取数据
                    $sql = "SELECT * FROM `25_zfb_data` WHERE {$whereClause} ORDER BY gmt_create DESC LIMIT {$limit} OFFSET {$offset}";
                    $stmt = $db->query($sql, $params);
                    $data = $stmt->fetchAll();

                    $response = [
                        'success' => true,
                        'data' => $data,
                        'total' => $total,
                        'page' => $page,
                        'totalPages' => $totalPages,
                        'limit' => $limit
                    ];
                } catch (Exception $e) {
                    $response = ['success' => false, 'message' => $e->getMessage()];
                }
                break;

            case 'get_status_list':
                // 获取交易状态列表
                try {
                    $db = Connection::getInstance();
                    $statusSql = "SELECT DISTINCT trade_status FROM `25_zfb_data` WHERE trade_status IS NOT NULL ORDER BY trade_status";
                    $statusList = $db->query($statusSql)->fetchAll();
                    $response = ['success' => true, 'data' => $statusList];
                } catch (Exception $e) {
                    $response = ['success' => false, 'message' => $e->getMessage()];
                }
                break;

            default:
                $response = ['success' => false, 'message' => '未知操作'];
        }
        
        echo json_encode($response);
        exit;
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝数据同步系统 - 管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab:hover {
            background: #e9ecef;
            color: #333;
        }

        .tab.active {
            background: white;
            color: #007bff;
            border-bottom-color: #007bff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-label {
            color: #666;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .logs-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .log-item.error {
            border-left-color: #dc3545;
        }
        
        .log-item.running {
            border-left-color: #ffc107;
        }
        
        .log-time {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .log-content {
            color: #333;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }

        /* 数据查看页面样式 */
        .filters {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .stats {
            padding: 15px 20px;
            background: #e3f2fd;
            color: #1565c0;
            font-weight: 500;
        }

        .table-container {
            overflow-x: auto;
            max-height: 600px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .amount {
            font-weight: bold;
            color: #28a745;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .pagination {
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
        }

        .pagination a,
        .pagination span {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 2px;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
            cursor: pointer;
        }

        .pagination a:hover {
            background: #e9ecef;
        }

        .pagination .current {
            background: #007bff;
            color: white;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .actions {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }

            .tabs {
                flex-direction: column;
            }

            .tab {
                border-bottom: 1px solid #ddd;
                border-right: none;
            }

            .tab.active {
                border-bottom-color: #007bff;
                border-right-color: transparent;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 支付宝数据同步系统</h1>
            <p>Web管理界面 - 合并版本</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="switchTab('dashboard')">🏠 管理首页</button>
            <button class="tab" onclick="switchTab('data')">📊 数据查看</button>
        </div>

        <div class="tab-content active" id="dashboard-content">
            <div class="main-content">
            <div class="alert alert-success" id="success-alert"></div>
            <div class="alert alert-error" id="error-alert"></div>
            
            <div class="dashboard">
                <div class="card">
                    <h3>📊 数据统计</h3>
                    <div id="statistics-content">
                        <div class="loading">正在加载统计信息...</div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>🔄 系统状态</h3>
                    <div id="system-status">
                        <div class="loading">正在检查系统状态...</div>
                    </div>
                </div>
            </div>
            
            <div class="actions">
                <button class="btn btn-success" onclick="manualSync()">🔄 手动同步</button>
                <button class="btn btn-info" onclick="checkSystem()">🔍 检查系统</button>
                <button class="btn btn-warning" onclick="refreshLogs()">📋 刷新日志</button>
                <button class="btn" onclick="refreshStats()">📊 刷新统计</button>
            </div>
            
            <div class="card">
                <h3>📋 同步日志</h3>
                <div class="logs-container" id="logs-container">
                    <div class="loading">正在加载同步日志...</div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="data-content">
            <div class="filters">
                <div class="filter-row">
                    <div class="form-group">
                        <label>搜索关键词</label>
                        <input type="text" id="search-input" placeholder="商品标题、订单号、账户...">
                    </div>

                    <div class="form-group">
                        <label>交易状态</label>
                        <select id="status-select">
                            <option value="">全部状态</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>开始日期</label>
                        <input type="date" id="date-from">
                    </div>

                    <div class="form-group">
                        <label>结束日期</label>
                        <input type="date" id="date-to">
                    </div>

                    <div class="form-group">
                        <button type="button" class="btn" onclick="searchData()">🔍 搜索</button>
                    </div>
                </div>
            </div>

            <div class="stats" id="data-stats">
                📈 正在加载数据...
            </div>

            <div class="table-container" id="data-table-container">
                <div class="loading">正在加载数据...</div>
            </div>

            <div class="pagination" id="data-pagination"></div>
        </div>

        <div class="footer">
            <p>版本：2.0</p>
        </div>
    </div>

    <script>
        // 全局变量
        let currentDataPage = 1;
        let currentDataFilters = {};
        let autoRefreshInterval;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadSyncLogs();
            checkSystemStatus();
            loadStatusList();

            // 每30秒自动刷新一次（仅在管理首页）
            startAutoRefresh();
        });

        // 标签页切换
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容区域
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-content').classList.add('active');

            // 根据标签页设置自动刷新
            if (tabName === 'dashboard') {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
                if (tabName === 'data') {
                    loadData();
                }
            }
        }

        // 开始自动刷新
        function startAutoRefresh() {
            stopAutoRefresh();
            autoRefreshInterval = setInterval(function() {
                if (document.getElementById('dashboard-content').classList.contains('active')) {
                    loadStatistics();
                    loadSyncLogs();
                }
            }, 30000);
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
        
        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertId = type === 'success' ? 'success-alert' : 'error-alert';
            const alertElement = document.getElementById(alertId);
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }
        
        // 发送AJAX请求
        function sendRequest(action, callback) {
            const formData = new FormData();
            formData.append('action', action);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (callback) callback(data);
            })
            .catch(error => {
                console.error('请求失败:', error);
                showAlert('请求失败: ' + error.message, 'error');
            });
        }
        
        // 加载统计信息
        function loadStatistics() {
            sendRequest('get_statistics', function(response) {
                if (response.success) {
                    const stats = response.data;
                    const html = `
                        <div class="stat-item">
                            <span class="stat-label">总记录数</span>
                            <span class="stat-value">${stats.total_records.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">今日新增</span>
                            <span class="stat-value">${stats.today_records.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功交易</span>
                            <span class="stat-value">${stats.success_count.toLocaleString()} 笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功金额</span>
                            <span class="stat-value">¥${parseFloat(stats.success_amount).toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最新记录</span>
                            <span class="stat-value">${stats.latest_time || 'N/A'}</span>
                        </div>
                    `;
                    document.getElementById('statistics-content').innerHTML = html;
                } else {
                    document.getElementById('statistics-content').innerHTML = '<div class="loading">加载失败</div>';
                }
            });
        }
        
        // 加载同步日志
        function loadSyncLogs() {
            sendRequest('get_sync_logs', function(response) {
                if (response.success) {
                    const logs = response.data;
                    let html = '';
                    
                    logs.forEach(log => {
                        const statusClass = log.status === 'success' ? '' : (log.status === 'failed' ? 'error' : 'running');
                        const statusIcon = log.status === 'success' ? '✅' : (log.status === 'failed' ? '❌' : '🔄');
                        
                        html += `
                            <div class="log-item ${statusClass}">
                                <div class="log-time">${log.created_at}</div>
                                <div class="log-content">
                                    ${statusIcon} ${log.sync_type} - ${log.status}<br>
                                    时间范围: ${log.start_time} ~ ${log.end_time}<br>
                                    记录数: ${log.total_records} (新增: ${log.new_records}, 更新: ${log.updated_records}, 错误: ${log.error_records})<br>
                                    执行时间: ${log.execution_time} 秒
                                    ${log.error_message ? '<br>错误: ' + log.error_message : ''}
                                </div>
                            </div>
                        `;
                    });
                    
                    document.getElementById('logs-container').innerHTML = html || '<div class="loading">暂无日志</div>';
                } else {
                    document.getElementById('logs-container').innerHTML = '<div class="loading">加载失败</div>';
                }
            });
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            sendRequest('check_system', function(response) {
                const statusHtml = response.success ? 
                    '<div class="stat-item"><span class="stat-label">系统状态</span><span class="stat-value" style="color: #28a745;">✅ 正常</span></div>' :
                    '<div class="stat-item"><span class="stat-label">系统状态</span><span class="stat-value" style="color: #dc3545;">❌ 异常</span></div>';
                
                document.getElementById('system-status').innerHTML = statusHtml;
            });
        }
        
        // 手动同步
        function manualSync() {
            const btn = event.target;
            btn.disabled = true;
            btn.textContent = '🔄 同步中...';
            
            sendRequest('manual_sync', function(response) {
                btn.disabled = false;
                btn.textContent = '🔄 手动同步';
                
                if (response.success) {
                    showAlert('手动同步成功！', 'success');
                    loadStatistics();
                    loadSyncLogs();
                } else {
                    showAlert('手动同步失败: ' + response.message, 'error');
                }
            });
        }
        
        // 检查系统
        function checkSystem() {
            checkSystemStatus();
            showAlert('系统状态已刷新', 'success');
        }
        
        // 刷新日志
        function refreshLogs() {
            loadSyncLogs();
            showAlert('日志已刷新', 'success');
        }
        
        // 刷新统计
        function refreshStats() {
            loadStatistics();
            showAlert('统计信息已刷新', 'success');
        }

        // 加载状态列表
        function loadStatusList() {
            sendRequest('get_status_list', function(response) {
                if (response.success) {
                    const statusSelect = document.getElementById('status-select');
                    statusSelect.innerHTML = '<option value="">全部状态</option>';

                    response.data.forEach(status => {
                        const option = document.createElement('option');
                        option.value = status.trade_status;
                        option.textContent = status.trade_status;
                        statusSelect.appendChild(option);
                    });
                }
            });
        }

        // 搜索数据
        function searchData() {
            currentDataPage = 1;
            currentDataFilters = {
                search: document.getElementById('search-input').value,
                status: document.getElementById('status-select').value,
                date_from: document.getElementById('date-from').value,
                date_to: document.getElementById('date-to').value
            };
            loadData();
        }

        // 加载数据
        function loadData(page = 1) {
            currentDataPage = page;

            const formData = new FormData();
            formData.append('action', 'get_data');
            formData.append('page', page);
            formData.append('search', currentDataFilters.search || '');
            formData.append('status', currentDataFilters.status || '');
            formData.append('date_from', currentDataFilters.date_from || '');
            formData.append('date_to', currentDataFilters.date_to || '');

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderDataTable(data);
                    renderPagination(data);
                    updateDataStats(data);
                } else {
                    document.getElementById('data-table-container').innerHTML =
                        '<div class="no-data"><h3>😔 加载失败</h3><p>' + data.message + '</p></div>';
                }
            })
            .catch(error => {
                console.error('加载数据失败:', error);
                document.getElementById('data-table-container').innerHTML =
                    '<div class="no-data"><h3>😔 加载失败</h3><p>网络错误</p></div>';
            });
        }

        // 渲染数据表格
        function renderDataTable(data) {
            if (!data.data || data.data.length === 0) {
                document.getElementById('data-table-container').innerHTML =
                    '<div class="no-data"><h3>😔 暂无数据</h3><p>请尝试调整搜索条件</p></div>';
                return;
            }

            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>创建时间</th>
                            <th>商品标题</th>
                            <th>交易金额</th>
                            <th>对方账户</th>
                            <th>交易状态</th>
                            <th>支付宝订单号</th>
                            <th>商户订单号</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.data.forEach(row => {
                const statusClass = row.trade_status === '成功' ? 'status-success' :
                    (row.trade_status === '失败' ? 'status-failed' : 'status-pending');

                html += `
                    <tr>
                        <td>${row.gmt_create || '-'}</td>
                        <td>${row.goods_title || '-'}</td>
                        <td class="amount">¥${parseFloat(row.total_amount || 0).toLocaleString('zh-CN', {minimumFractionDigits: 2})}</td>
                        <td>${row.other_account || '-'}</td>
                        <td><span class="status ${statusClass}">${row.trade_status || '-'}</span></td>
                        <td>${row.alipay_order_no || '-'}</td>
                        <td>${row.merchant_order_no || '-'}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            document.getElementById('data-table-container').innerHTML = html;
        }

        // 渲染分页
        function renderPagination(data) {
            if (data.totalPages <= 1) {
                document.getElementById('data-pagination').innerHTML = '';
                return;
            }

            let html = '';

            // 上一页
            if (data.page > 1) {
                html += `<span onclick="loadData(${data.page - 1})">« 上一页</span>`;
            }

            // 页码
            const start = Math.max(1, data.page - 2);
            const end = Math.min(data.totalPages, data.page + 2);

            for (let i = start; i <= end; i++) {
                if (i === data.page) {
                    html += `<span class="current">${i}</span>`;
                } else {
                    html += `<span onclick="loadData(${i})">${i}</span>`;
                }
            }

            // 下一页
            if (data.page < data.totalPages) {
                html += `<span onclick="loadData(${data.page + 1})">下一页 »</span>`;
            }

            document.getElementById('data-pagination').innerHTML = html;
        }

        // 更新数据统计
        function updateDataStats(data) {
            const statsText = `📈 共找到 ${data.total.toLocaleString()} 条记录，当前第 ${data.page}/${data.totalPages} 页`;
            document.getElementById('data-stats').innerHTML = statsText;
        }
    </script>
</body>
</html>
